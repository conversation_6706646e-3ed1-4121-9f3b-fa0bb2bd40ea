import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import swaggerUi from 'swagger-ui-express';
import redisClient, { connectRedis } from './src/config/redis';
import swaggerSpec from './src/config/swagger';
import routes from './src/routes';
import { errorHandler } from './src/utils/errorHandler';
import prisma from './src/config/database';
import { requestLogger } from './src/middlewares/logging.middleware';

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(requestLogger);

// API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Health check endpoint
app.get('/health', (_req: express.Request, res: express.Response) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// API Routes
app.use('/api', routes);

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  errorHandler(err, req, res, next);
});

// Connect to database and Redis
const startServer = async () => {
  let databaseConnected = false;
  let redisConnected = false;

  try {
    // Connect to Redis (non-blocking)
    connectRedis()
      .then(() => {
        redisConnected = true;
        console.log('Redis connection established');
      })
      .catch((error) => {
        console.warn('Redis connection failed, continuing without Redis:', error.message);
        console.log('Some features like caching may be limited');
      });

    // Test database connection (blocking - we need this to work)
    try {
      await prisma.$connect();
      console.log('Database connected successfully to Neon PostgreSQL');
      databaseConnected = true;
    
    } catch (dbError) {
      console.error('Critical error: Database connection failed:', dbError);
      throw new Error('Database connection failed');
    }

    // Start server only if database is connected
    if (databaseConnected) {
      app.listen(PORT, () => {
        console.log(`Server running on port ${PORT}`);
        console.log(`API Documentation available at http://localhost:${PORT}/api-docs`);
        console.log(`Database connected: ${databaseConnected}, Redis connected: ${redisConnected}`);
        console.log(`Firebase Authentication enabled for Riders and Admins`);
      });
    } else {
      throw new Error('Cannot start server without database connection');
    }
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Handle graceful shutdown
process.on('SIGINT', async () => {
  try {
    // Disconnect from database
    await prisma.$disconnect();
    console.log('Database disconnected');

    // Disconnect from Redis if connected
    if (redisClient.isOpen) {
      await redisClient.quit();
      console.log('Redis disconnected');
    }
  } catch (error) {
    console.error('Error during shutdown:', error);
  } finally {
    console.log('Server shutdown complete');
    process.exit(0);
  }
});

process.on('SIGTERM', async () => {
  try {
    // Disconnect from database
    await prisma.$disconnect();
    console.log('Database disconnected');

    // Disconnect from Redis if connected
    if (redisClient.isOpen) {
      await redisClient.quit();
      console.log('Redis disconnected');
    }
  } catch (error) {
    console.error('Error during shutdown:', error);
  } finally {
    console.log('Server shutdown complete');
    process.exit(0);
  }
});

// Start the server if not in test mode
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

// Export app for testing
export default app;