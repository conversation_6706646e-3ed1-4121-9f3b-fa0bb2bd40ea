services:
  - type: web
    name: kraft-backend
    env: node
    buildCommand: npm install && npm run prisma:generate
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: DATABASE_URL
        sync: false
      - key: REDIS_URL
        sync: false

      # Firebase configurations - Rider
      - key: RIDER_FIREBASE_PROJECT_ID
        sync: false
      - key: RIDER_FIREBASE_PRIVATE_KEY
        sync: false
      - key: RIDER_FIREBASE_CLIENT_EMAIL
        sync: false
      - key: RIDER_FIREBASE_API_KEY
        sync: false

      # Firebase configurations - Admin
      - key: ADMIN_FIREBASE_PROJECT_ID
        sync: false
      - key: ADMIN_FIREBASE_PRIVATE_KEY
        sync: false
      - key: ADMIN_FIREBASE_CLIENT_EMAIL
        sync: false
      - key: ADMIN_FIREBASE_API_KEY
        sync: false

      # Other configurations
      - key: JWT_SECRET
        sync: false
      - key: JWT_EXPIRES_IN
        value: 1d
      - key: FRONTEND_URL
        sync: false
      - key: DEFAULT_ADMIN_EMAIL
        sync: false

      # Email configuration
      - key: SMTP_HOST
        sync: false
      - key: SMTP_PORT
        sync: false
      - key: SMTP_SECURE
        sync: false
      - key: SMTP_USER
        sync: false
      - key: SMTP_PASS
        sync: false
      - key: SMTP_FROM
        sync: false

      # API Keys
      - key: GST_API_KEY
        sync: false
    autoDeploy: true
