# Kraft Paper Application Backend

Backend API for the Kraft Paper Application, a platform for managing kraft paper inventory, orders, and customer interactions.

## Features

- User authentication and authorization
- Stock inventory management
- Shopping cart functionality
- Order processing and management
- Customer enquiry system
- Notifications
- Admin dashboard and analytics
- Tally integration for financial management

## Tech Stack

- **Node.js** - JavaScript runtime
- **Express.js** - Web framework
- **TypeScript** - Type-safe JavaScript
- **PostgreSQL** - Relational database
- **Prisma** - ORM for database access
- **Redis** - Caching and session management
- **JWT** - Authentication
- **Joi** - Input validation
- **Firebase Admin** - Phone OTP authentication
- **Docker** - Containerization
- **Swagger** - API documentation

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Docker and Docker Compose (for containerized setup)
- PostgreSQL (if running locally)
- Redis (if running locally)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/harshit3478/kraft-backend.git
   cd kraft-backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

4. Generate Prisma client:
   ```bash
   npm run prisma:generate
   ```

5. Run database migrations:
   ```bash
   npm run prisma:migrate
   ```

### Running the Application

#### Using Docker (recommended)

```bash
# Start all services
npm run docker:up

# Stop all services
npm run docker:down
```

#### Local Development

```bash
# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### API Documentation

API documentation is available at `/api-docs` when the server is running.

## Deployment

The application is configured for automatic deployment to EC2 using GitHub Actions with a self-hosted runner.

### Prerequisites for Deployment

- EC2 instance with GitHub Actions self-hosted runner configured
- Node.js 18+ installed on the EC2 instance
- PM2 installed globally on the EC2 instance
- PostgreSQL database accessible from the EC2 instance
- Redis server (optional, will fallback gracefully if not available)

### Deployment Process

The deployment is triggered automatically when code is pushed to the `main` or `deve` branches. The workflow:

1. **Checks out the latest code** from the repository
2. **Sets up Node.js environment** and caches dependencies
3. **Creates environment file** with production secrets
4. **Installs dependencies** using `npm ci`
5. **Generates Prisma client** for database access
6. **Runs database migrations** to ensure schema is up-to-date
7. **Builds the application** using TypeScript compiler
8. **Manages the application with PM2**:
   - Stops any existing `kraft-server` process
   - Starts the application using `npm run dev`
   - Configures PM2 for auto-restart on system reboot
9. **Performs health checks** to ensure the application is running
10. **Shows deployment status** and process information

### PM2 Process Management

The application runs as a PM2 process named `kraft-server` with the following features:

- **Auto-restart**: Automatically restarts if the process crashes
- **Memory monitoring**: Restarts if memory usage exceeds 1GB
- **Logging**: Centralized logs in the `logs/` directory
- **System startup**: Automatically starts when the EC2 instance reboots
- **Health monitoring**: Built-in process monitoring and restart policies

### Manual Deployment Commands

If you need to deploy manually on the EC2 instance:

```bash
# Stop the current process
pm2 stop kraft-server

# Pull latest code
git pull origin main

# Install dependencies and build
npm ci
npm run prisma:generate
npm run prisma:deploy
npm run build

# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
```

### Health Monitoring

The application provides a health endpoint at `/health` that checks:
- Application uptime
- Database connectivity
- Redis connectivity (optional)

You can monitor the application health by visiting `http://your-ec2-ip:5000/health`.

## Project Structure

```
kraft-backend/
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Request handlers
│   ├── middlewares/    # Express middlewares
│   ├── models/         # Data models
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── utils/          # Utility functions
│   └── validations/    # Input validation schemas
├── prisma/             # Prisma schema and migrations
├── .github/workflows/  # GitHub Actions workflows
├── logs/               # PM2 application logs
├── .env.example        # Example environment variables
├── .eslintrc.json      # ESLint configuration
├── .prettierrc         # Prettier configuration
├── docker-compose.yml  # Docker Compose configuration
├── Dockerfile          # Docker configuration
├── ecosystem.config.js # PM2 process configuration
├── package.json        # Project dependencies
└── tsconfig.json       # TypeScript configuration
```

## License

This project is licensed under the ISC License.

## Author

Harshit Agarwal
