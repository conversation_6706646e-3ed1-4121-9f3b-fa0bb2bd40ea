import { Router } from 'express';
import { authenticateAdmin, isAdmin } from '../middlewares/auth.middleware';
import validate from '../middlewares/validate.middleware';
import { rejectUserSchema, userQuerySchema } from '../validations/admin.validation';
import { orderStatusUpdateSchema, paymentStatusUpdateSchema, orderQuerySchema } from '../validations/order.validation';
import { enquiryResponseSchema, enquiryQuerySchema } from '../validations/enquiry.validation';
import { dashboardQuerySchema, topProductsQuerySchema, customerLedgerParamSchema, syncInvoiceParamSchema } from '../validations/dashboard.validation';
import { paymentTermUpgradeQuerySchema, updatePaymentTermUpgradeStatusSchema } from '../validations/paymentTermUpgrade.validation';
import { supportQueryQuerySchema, respondToSupportQuerySchema } from '../validations/supportQuery.validation';
import { getUsers, approveUser, rejectUser, getUserById } from '../controllers/admin.controller';
import { getAllOrdersHandler, updateOrderStatusHandler, updatePaymentStatusHandler, getOrderDetailsHandler } from '../controllers/order.controller';
import { getAllEnquiriesHandler, respondToEnquiryHandler, getEnquiry } from '../controllers/enquiry.controller';
import {
  getDashboardStats,
  getSalesOverviewHandler,
  getTopSellingProductsHandler,
  getCustomerStatsHandler,
  getInventoryStatsHandler,
  getCustomerLedgerHandler,
  syncInvoiceHandler
} from '../controllers/dashboard.controller';
import {
  getAllPaymentTermUpgradeRequestsHandler,
  getPaymentTermUpgradeRequest,
  updatePaymentTermUpgradeRequestStatusHandler,
} from '../controllers/paymentTermUpgrade.controller';
import {
  getAllSupportQueriesHandler,
  getSupportQuery,
  respondToSupportQueryHandler,
} from '../controllers/supportQuery.controller';
import { asyncHandler } from '../utils/errorHandler';

const router = Router();

// All routes require admin authentication
router.use(authenticateAdmin, isAdmin);


// Keep all the other existing admin routes below this line
// ...

// export default router;
/**
 * Order routes for admin
 */
router.get('/orders', validate(orderQuerySchema, 'query'), getAllOrdersHandler);
router.get('/orders/:id', getOrderDetailsHandler);
router.patch('/orders/:id/status', validate(orderStatusUpdateSchema), updateOrderStatusHandler);
router.patch('/orders/:id/payment-status', validate(paymentStatusUpdateSchema), updatePaymentStatusHandler);


// Enquiry routes for admin
router.get('/enquiries', validate(enquiryQuerySchema, 'query'), getAllEnquiriesHandler);
router.get('/enquiries/:id', getEnquiry);
router.patch('/enquiries/:id/respond', validate(enquiryResponseSchema), respondToEnquiryHandler);

// Dashboard routes for admin
router.get('/dashboard', validate(dashboardQuerySchema, 'query'), getDashboardStats);
router.get('/analytics/sales', validate(dashboardQuerySchema, 'query'), getSalesOverviewHandler);
router.get('/analytics/products', validate(topProductsQuerySchema, 'query'), getTopSellingProductsHandler);
router.get('/analytics/customers', validate(dashboardQuerySchema, 'query'), getCustomerStatsHandler);
router.get('/analytics/inventory', getInventoryStatsHandler);

// Tally routes for admin
router.get('/tally/ledger/:gstNumber', validate(customerLedgerParamSchema, 'params'), getCustomerLedgerHandler);
router.get('/tally/invoice/:orderId', validate(syncInvoiceParamSchema, 'params'), syncInvoiceHandler);

// Payment term upgrade request routes for admin
router.get('/payment-term-upgrades', validate(paymentTermUpgradeQuerySchema, 'query'), getAllPaymentTermUpgradeRequestsHandler);
router.get('/payment-term-upgrades/:id', getPaymentTermUpgradeRequest);
router.put('/payment-term-upgrades/:id/status', validate(updatePaymentTermUpgradeStatusSchema), updatePaymentTermUpgradeRequestStatusHandler);

// Support query routes for admin
router.get('/support-queries', validate(supportQueryQuerySchema, 'query'), getAllSupportQueriesHandler);
router.get('/support-queries/:id', getSupportQuery);
router.patch('/support-queries/:id/respond', validate(respondToSupportQuerySchema), respondToSupportQueryHandler);

// User routes for admin
router.get('/users', validate(userQuerySchema) , getUsers);
router.get('/users/:id', getUserById);
router.patch('/users/:id/approve', approveUser);
router.patch('/users/:id/reject', validate(rejectUserSchema), rejectUser);

export default router;
