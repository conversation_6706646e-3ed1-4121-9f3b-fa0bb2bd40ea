import { Router } from 'express';
import { authenticateRider } from '../middlewares/auth.middleware';
import validate from '../middlewares/validate.middleware';
import {
  createPaymentTermUpgradeRequestSchema,
  paymentTermUpgradeQuerySchema,
} from '../validations/paymentTermUpgrade.validation';
import {
  getPaymentTermUpgradeRequests,
  getPaymentTermUpgradeRequest,
  createPaymentTermUpgradeRequestHandler,
} from '../controllers/paymentTermUpgrade.controller';

const router = Router();

// All routes require rider authentication
router.use(authenticateRider);

/**
 * @swagger
 * /api/payment-term-upgrades:
 *   get:
 *     summary: Get user's payment term upgrade requests
 *     tags: [Payment Term Upgrades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, APPROVED, REJECTED]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Payment term upgrade requests retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', validate(paymentTermUpgradeQuerySchema, 'query'), getPaymentTermUpgradeRequests);

/**
 * @swagger
 * /api/payment-term-upgrades/{id}:
 *   get:
 *     summary: Get payment term upgrade request by ID
 *     tags: [Payment Term Upgrades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment term upgrade request ID
 *     responses:
 *       200:
 *         description: Payment term upgrade request retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Payment term upgrade request not found
 */
router.get('/:id', getPaymentTermUpgradeRequest);

/**
 * @swagger
 * /api/payment-term-upgrades:
 *   post:
 *     summary: Create new payment term upgrade request
 *     tags: [Payment Term Upgrades]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *               - requestedPaymentTerms
 *             properties:
 *               reason:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 500
 *                 description: Reason for requesting payment term upgrade
 *               requestedPaymentTerms:
 *                 type: string
 *                 enum: [THIRTY_DAYS, SIXTY_DAYS]
 *                 description: Requested payment terms
 *     responses:
 *       201:
 *         description: Payment term upgrade request created successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 */
router.post('/', validate(createPaymentTermUpgradeRequestSchema), createPaymentTermUpgradeRequestHandler);

export default router;
