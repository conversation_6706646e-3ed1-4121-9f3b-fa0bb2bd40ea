import { Router } from 'express';
import { authenticateRider, authenticateAdmin } from '../middlewares/auth.middleware';
import {
  getNotifications,
  mark<PERSON><PERSON><PERSON>,
  markAllAsRead,
  deleteNotificationHandler,
  getAdminNotifications,
  updateFCMToken,
  getUnreadCount,
  sendBulkNotification,
} from '../controllers/notification.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Notifications
 *   description: Notification management
 */

/**
 * @swagger
 * /api/notifications:
 *   get:
 *     summary: Get user's notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *         description: Filter to show only unread notifications
 *     responses:
 *       200:
 *         description: List of notifications
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticateRider, getNotifications);

/**
 * @swagger
 * /api/notifications/{id}/read:
 *   patch:
 *     summary: Mark notification as read
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification marked as read
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Notification not found
 */
router.patch('/:id/read', authenticateRider, markAsRead);

/**
 * @swagger
 * /api/notifications/read-all:
 *   patch:
 *     summary: Mark all notifications as read
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All notifications marked as read
 *       401:
 *         description: Unauthorized
 */
router.patch('/read-all', authenticateRider, markAllAsRead);

/**
 * @swagger
 * /api/notifications/{id}:
 *   delete:
 *     summary: Delete notification
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Notification not found
 */
router.delete('/:id', authenticateRider, deleteNotificationHandler);

/**
 * @swagger
 * /api/notifications/admin:
 *   get:
 *     summary: Get system admin notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *         description: Filter to show only unread notifications
 *     responses:
 *       200:
 *         description: List of system admin notifications
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/admin', authenticateAdmin, getAdminNotifications);

/**
 * @swagger
 * /api/notifications/admin/{id}/read:
 *   patch:
 *     summary: Mark admin notification as read
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification marked as read
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Notification not found
 */
router.patch('/admin/:id/read', authenticateAdmin, (req, res, next) => {
  // Set admin flag but keep actual user ID
  req.user = { ...req.user, isAdminUser: true };
  markAsRead(req, res, next);
});

/**
 * @swagger
 * /api/notifications/admin/{id}:
 *   delete:
 *     summary: Delete admin notification
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Notification not found
 */
router.delete('/admin/:id', authenticateAdmin, (req, res, next) => {
  // Set admin flag but keep actual user ID
  req.user = { ...req.user, isAdminUser: true };
  deleteNotificationHandler(req, res, next);
});

/**
 * @swagger
 * /api/notifications/fcm-token:
 *   put:
 *     summary: Update FCM token for user
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: FCM token for push notifications
 *             required:
 *               - fcmToken
 *     responses:
 *       200:
 *         description: FCM token updated successfully
 *       400:
 *         description: FCM token is required
 *       401:
 *         description: Unauthorized
 */
router.put('/fcm-token', authenticateRider, updateFCMToken);

/**
 * @swagger
 * /api/notifications/unread-count:
 *   get:
 *     summary: Get unread notification count
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Unread notification count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     unreadCount:
 *                       type: integer
 *                       example: 5
 *       401:
 *         description: Unauthorized
 */
router.get('/unread-count', authenticateRider, getUnreadCount);

/**
 * @swagger
 * /api/notifications/bulk-send:
 *   post:
 *     summary: Send bulk promotional notification (Admin only)
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: Notification title
 *               message:
 *                 type: string
 *                 description: Notification message
 *             required:
 *               - title
 *               - message
 *     responses:
 *       200:
 *         description: Bulk notification sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Bulk notification sent successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     notificationsCreated:
 *                       type: integer
 *                     usersWithFCMTokens:
 *                       type: integer
 *       400:
 *         description: Title and message are required
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 */
router.post('/bulk-send', authenticateAdmin, sendBulkNotification);

export default router;
