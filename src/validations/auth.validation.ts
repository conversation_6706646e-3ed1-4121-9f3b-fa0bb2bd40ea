import Joi from 'joi';

// GST Number validation regex pattern
const gstPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

// Email OTP pattern (6 digits)
const otpPattern = /^\d{6}$/;

// Registration validation schema
export const registerSchema = Joi.object({
  gstNumber: Joi.string().pattern(gstPattern).required()
    .messages({
      'string.pattern.base': 'Please enter a valid GST number',
      'any.required': 'GST number is required',
    }),
  companyName: Joi.string().min(3).max(100).required()
    .messages({
      'string.min': 'Company name must be at least 3 characters long',
      'string.max': 'Company name cannot exceed 100 characters',
      'any.required': 'Company name is required',
    }),
  contactPerson: Joi.string().min(3).max(50).required()
    .messages({
      'string.min': 'Contact person name must be at least 3 characters long',
      'string.max': 'Contact person name cannot exceed 50 characters',
      'any.required': 'Contact person name is required',
    }),
  contactNumber: Joi.string().pattern(/^[6-9]\d{9}$/).required()
    .messages({
      'string.pattern.base': 'Please enter a valid 10-digit Indian mobile number',
      'any.required': 'Contact number is required',
    }),
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email is required',
    }),
  firebaseUid: Joi.string().optional(),
  password: Joi.string().min(8).required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'any.required': 'Password is required',
    }),
  paymentTerms: Joi.string().valid('IMMEDIATE', 'THIRTY_DAYS', 'SIXTY_DAYS').required()
    .messages({
      'any.only': 'Payment terms must be one of: IMMEDIATE, THIRTY_DAYS, SIXTY_DAYS',
      'any.required': 'Payment terms are required',
    }),
});

// JWT Registration validation schema (no password required)
export const jwtRegisterSchema = Joi.object({
  gstNumber: Joi.string().pattern(gstPattern).required()
    .messages({
      'string.pattern.base': 'Please enter a valid GST number',
      'any.required': 'GST number is required',
    }),
  companyName: Joi.string().min(3).max(100).required()
    .messages({
      'string.min': 'Company name must be at least 3 characters long',
      'string.max': 'Company name cannot exceed 100 characters',
      'any.required': 'Company name is required',
    }),
  contactPerson: Joi.string().min(3).max(50).required()
    .messages({
      'string.min': 'Contact person name must be at least 3 characters long',
      'string.max': 'Contact person name cannot exceed 50 characters',
      'any.required': 'Contact person name is required',
    }),
  contactNumber: Joi.string().pattern(/^[6-9]\d{9}$/).required()
    .messages({
      'string.pattern.base': 'Please enter a valid 10-digit Indian mobile number',
      'any.required': 'Contact number is required',
    }),
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email is required',
    }),
  paymentTerms: Joi.string().valid('IMMEDIATE', 'THIRTY_DAYS', 'SIXTY_DAYS').required()
    .messages({
      'any.only': 'Payment terms must be one of: IMMEDIATE, THIRTY_DAYS, SIXTY_DAYS',
      'any.required': 'Payment terms are required',
    }),
});

// Login validation schema
export const loginSchema = Joi.object({
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email is required',
    }),
  password: Joi.string().required()
    .messages({
      'any.required': 'Password is required',
    }),
});

// OTP verification schema
export const otpVerificationSchema = Joi.object({
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email is required',
    }),
  code: Joi.string().length(6).pattern(/^\d+$/).required()
    .messages({
      'string.length': 'OTP must be 6 digits',
      'string.pattern.base': 'OTP must contain only digits',
      'any.required': 'OTP is required',
    }),
});

// Phone OTP schema
export const phoneOTPSchema = Joi.object({
  phoneNumber: Joi.string().pattern(/^[6-9]\d{9}$/).required()
    .messages({
      'string.pattern.base': 'Please enter a valid 10-digit Indian mobile number',
      'any.required': 'Phone number is required',
    }),
});

// Phone verification schema
export const phoneVerificationSchema = Joi.object({
  verificationId: Joi.string().required()
    .messages({
      'any.required': 'Verification ID is required',
    }),
  code: Joi.string().required()
    .messages({
      'any.required': 'OTP code is required',
    }),
  phoneNumber: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).required()
    .messages({
      'string.pattern.base': 'Please enter a valid phone number with country code (e.g., +91XXXXXXXXXX)',
      'any.required': 'Phone number is required',
    }),
});

// Forgot password schema
export const forgotPasswordSchema = Joi.object({
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email is required',
    }),
});

// Reset password schema
export const resetPasswordSchema = Joi.object({
  token: Joi.string().required()
    .messages({
      'any.required': 'Token is required',
    }),
  password: Joi.string().min(8).required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'any.required': 'Password is required',
    }),
});

// Email OTP schema
export const emailOTPSchema = Joi.object({
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email is required',
    }),
});

// Email verification schema
export const emailVerificationSchema = Joi.object({
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'any.required': 'Email is required',
    }),
  otp: Joi.string().pattern(otpPattern).required()
    .messages({
      'string.pattern.base': 'OTP must be a 6-digit number',
      'any.required': 'OTP is required',
    }),
});
