import Joi from 'joi';

// Define order status and payment status values
const ORDER_STATUSES = ['PENDING', 'APPROVED', 'SHIPPED', 'DELIVERED', 'CANCELLED'];
const PAYMENT_STATUSES = ['PENDING', 'PAID', 'OVERDUE'];

// Create order validation schema
export const createOrderSchema = Joi.object({
  fulfillmentType: Joi.string().valid('STANDARD_SHIPPING', 'SELF_PICKUP').default('STANDARD_SHIPPING')
    .messages({
      'any.only': 'Fulfillment type must be either STANDARD_SHIPPING or SELF_PICKUP',
    }),
  shippingAddress: Joi.when('fulfillmentType', {
    is: 'STANDARD_SHIPPING',
    then: Joi.object({
      addressLine1: Joi.string().trim().required()
        .messages({
          'string.empty': 'Address line 1 is required',
          'any.required': 'Address line 1 is required',
        }),
      addressLine2: Joi.string().trim().allow('')
        .messages({
          'string.empty': 'Address line 2 cannot be empty',
        }),
      city: Joi.string().trim().required()
        .messages({
          'string.empty': 'City is required',
          'any.required': 'City is required',
        }),
      state: Joi.string().trim().required()
        .messages({
          'string.empty': 'State is required',
          'any.required': 'State is required',
        }),
      postalCode: Joi.string().trim().required()
        .messages({
          'string.empty': 'Postal code is required',
          'any.required': 'Postal code is required',
        }),
    }).required()
      .messages({
        'object.base': 'Shipping address must be an object',
        'any.required': 'Shipping address is required for standard shipping',
      }),
    otherwise: Joi.forbidden()
      .messages({
        'any.unknown': 'Shipping address is not allowed for self-pickup orders',
      }),
  }),
  vehicleNumber: Joi.when('fulfillmentType', {
    is: 'SELF_PICKUP',
    then: Joi.string().trim().required()
      .messages({
        'string.empty': 'Vehicle number is required for self-pickup',
        'any.required': 'Vehicle number is required for self-pickup',
      }),
    otherwise: Joi.forbidden()
      .messages({
        'any.unknown': 'Vehicle number is not allowed for standard shipping orders',
      }),
  }),
  notes: Joi.string().trim().allow('')
    .messages({
      'string.empty': 'Notes cannot be empty',
    }),
});

// Order query validation schema
export const orderQuerySchema = Joi.object({
  page: Joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  search: Joi.string().trim().allow('')
    .messages({
      'string.base': 'Search must be a string',
    }),
  status: Joi.string().valid(...ORDER_STATUSES)
    .messages({
      'any.only': `Status must be one of: ${ORDER_STATUSES.join(', ')}`,
    }),
  sortBy: Joi.string().valid('createdAt', 'totalAmount', 'orderNumber')
    .messages({
      'any.only': 'Sort by must be one of: createdAt, totalAmount, orderNumber',
    }),
  sortOrder: Joi.string().valid('asc', 'desc')
    .messages({
      'any.only': 'Sort order must be one of: asc, desc',
    }),
});

// Order status update validation schema (for admin)
export const orderStatusUpdateSchema = Joi.object({
  status: Joi.string().valid(...ORDER_STATUSES).required()
    .messages({
      'any.only': `Status must be one of: ${ORDER_STATUSES.join(', ')}`,
      'any.required': 'Status is required',
    }),
});

// Payment status update validation schema (for admin)
export const paymentStatusUpdateSchema = Joi.object({
  paymentStatus: Joi.string().valid(...PAYMENT_STATUSES).required()
    .messages({
      'any.only': `Payment status must be one of: ${PAYMENT_STATUSES.join(', ')}`,
      'any.required': 'Payment status is required',
    }),
});
