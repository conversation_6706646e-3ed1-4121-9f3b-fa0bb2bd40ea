import Joi from 'joi';

// Support query creation validation schema
export const createSupportQuerySchema = Joi.object({
  query: Joi.string().trim().min(10).max(1000).required()
    .messages({
      'string.empty': 'Query is required',
      'string.min': 'Query must be at least 10 characters long',
      'string.max': 'Query cannot exceed 1000 characters',
      'any.required': 'Query is required',
    }),
  email: Joi.string().email().optional()
    .messages({
      'string.email': 'Email must be a valid email address',
    }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional()
    .messages({
      'string.pattern.base': 'Phone number must be a valid phone number',
    }),
  name: Joi.string().trim().min(2).max(100).optional()
    .messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 100 characters',
    }),
});

// Support query query validation schema
export const supportQueryQuerySchema = Joi.object({
  page: Joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  status: Joi.string().valid('PENDING', 'RESPONDED', 'CLOSED')
    .messages({
      'any.only': 'Status must be one of: PENDING, RESPONDED, CLOSED',
    }),
  sortBy: Joi.string().valid('createdAt', 'updatedAt', 'status', 'respondedAt')
    .messages({
      'any.only': 'Sort by must be one of: createdAt, updatedAt, status, respondedAt',
    }),
  sortOrder: Joi.string().valid('asc', 'desc')
    .messages({
      'any.only': 'Sort order must be one of: asc, desc',
    }),
});

// Support query response validation schema (admin only)
export const respondToSupportQuerySchema = Joi.object({
  response: Joi.string().trim().min(10).max(1000).required()
    .messages({
      'string.empty': 'Response is required',
      'string.min': 'Response must be at least 10 characters long',
      'string.max': 'Response cannot exceed 1000 characters',
      'any.required': 'Response is required',
    }),
});

// Support query status update validation schema
export const updateSupportQueryStatusSchema = Joi.object({
  status: Joi.string().valid('CLOSED').required()
    .messages({
      'any.only': 'Status must be CLOSED',
      'any.required': 'Status is required',
    }),
});
