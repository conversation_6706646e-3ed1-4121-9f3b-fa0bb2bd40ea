// filepath: /home/<USER>/Dev/project-kraft/backend/src/config/firebase.ts
import admin from 'firebase-admin';

// Initialize Rider Firebase App
const riderFirebaseApp = admin.initializeApp({
  credential: admin.credential.cert({
    projectId: process.env.RIDER_FIREBASE_PROJECT_ID,
    privateKey: process.env.RIDER_FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    clientEmail: process.env.RIDER_FIREBASE_CLIENT_EMAIL,
  }),
}, 'rider');

// Initialize Admin Firebase App
const adminFirebaseApp = admin.initializeApp({
  credential: admin.credential.cert({
    projectId: process.env.ADMIN_FIREBASE_PROJECT_ID,
    privateKey: process.env.ADMIN_FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    clientEmail: process.env.ADMIN_FIREBASE_CLIENT_EMAIL,
  }),
}, 'admin');

export { riderFirebaseApp, adminFirebaseApp };
