import { PrismaClient, OrderStatus, PaymentStatus, PaymentTerms, EnquiryStatus, ContactPreference, NotificationType } from '@prisma/client';

// Fix for Prisma Client in tests
// See: https://github.com/prisma/prisma/issues/8291
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Configure Prisma client with additional options for cloud database
export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: ['error'], // Only log errors, not queries
    // Add connection pool configuration for cloud database
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

// Add connection event handlers if needed (Prisma Client v4+ does not support 'error' event)
// Removed query logging to reduce console noise

// prisma.$on('error', (e) => {
//   console.error('Prisma Error:', e);
// });

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Export enums for use in validation
export { OrderStatus, PaymentStatus,  PaymentTerms, EnquiryStatus, ContactPreference, NotificationType };

export default prisma;
