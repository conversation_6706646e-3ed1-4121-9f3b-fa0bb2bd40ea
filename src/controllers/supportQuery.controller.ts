import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getUserSupportQueries,
  getAllSupportQueries,
  getSupportQueryById,
  createSupportQuery,
  respondToSupportQuery,
  updateSupportQueryStatus,
} from '../services/supportQuery.service';
import { QueryStatus } from '@prisma/client';

/**
 * Get user's support queries
 */
export const getSupportQueries = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const {
    page,
    limit,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getUserSupportQueries(userId, {
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    status: status as QueryStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get all support queries (admin only)
 */
export const getAllSupportQueriesHandler = asyncHandler(async (req: Request, res: Response) => {
  const {
    page,
    limit,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getAllSupportQueries({
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    status: status as QueryStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get support query by ID
 */
export const getSupportQuery = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user?.role === 'RIDER' ? req.user.id : undefined;

  const query = await getSupportQueryById(id, userId);

  return res.status(200).json({
    status: 'success',
    data: { query },
  });
});

/**
 * Create new support query
 */
export const createSupportQueryHandler = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const { query, email, phone, name } = req.body;

  const supportQuery = await createSupportQuery(userId, {
    query,
    email,
    phone,
    name,
  });

  return res.status(201).json({
    status: 'success',
    message: 'Support query created successfully',
    data: { query: supportQuery },
  });
});

/**
 * Respond to support query (admin only)
 */
export const respondToSupportQueryHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { response } = req.body;

  const query = await respondToSupportQuery(id, response);

  return res.status(200).json({
    status: 'success',
    message: 'Support query responded to successfully',
    data: { query },
  });
});

/**
 * Update support query status
 */
export const updateSupportQueryStatusHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const { status } = req.body;

  const query = await updateSupportQueryStatus(id, userId, status);

  return res.status(200).json({
    status: 'success',
    message: `Support query ${status.toLowerCase()}`,
    data: { query },
  });
});
