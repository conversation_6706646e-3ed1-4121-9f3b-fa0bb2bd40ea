import { Request, Response, NextFunction } from 'express';
import prisma from '../config/database';
import { AppError, asyncHandler } from '../utils/errorHandler';
import { comparePassword, hashPassword } from '../utils/password';
import { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '../services/notification.service';
import { createNotification } from '../services/notification.service';

/**
 * Get user profile
 */
export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  const user = await prisma.rider.findUnique({
    where: { id: userId },
    select: {
      id: true,
      gstNumber: true,
      companyName: true,
      contactPerson: true,
      contactNumber: true,
      email: true,
      paymentTerms: true,
      isApproved: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  return res.status(200).json({
    status: 'success',
    data: { user },
  });
});

/**
 * Update user profile
 */
export const updateProfile = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user!.id;
  const { contactPerson, contactNumber, email } = req.body;

  // Check if email is already in use by another user
  if (email) {
    const existingUser = await prisma.rider.findFirst({
      where: {
        email,
        id: { not: userId },
      },
    });

    if (existingUser) {
      return next(new AppError('Email already in use', 409));
    }
  }

  // Update user profile
  const updatedUser = await prisma.rider.update({
    where: { id: userId },
    data: {
      contactPerson,
      contactNumber,
      email,
    },
    select: {
      id: true,
      gstNumber: true,
      companyName: true,
      contactPerson: true,
      contactNumber: true,
      email: true,
      paymentTerms: true,
      isApproved: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  return res.status(200).json({
    status: 'success',
    message: 'Profile updated successfully',
    data: { user: updatedUser },
  });
});

/**
 * Change user password
 */
export const changePassword = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user!.id;
  const { currentPassword, newPassword } = req.body;

  // Get user with password
  const user = await prisma.rider.findUnique({
    where: { id: userId },
    select: {
      id: true,
      password: true,
    },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Verify current password
  const isPasswordValid = await comparePassword(currentPassword, user.password);
  if (!isPasswordValid) {
    return next(new AppError('Current password is incorrect', 401));
  }

  // Hash new password
  const hashedPassword = await hashPassword(newPassword);

  // Update password
  await prisma.rider.update({
    where: { id: userId },
    data: {
      password: hashedPassword,
    },
  });

  // Create notification for password change
  await createNotification(
    userId,
    'ACCOUNT_APPROVAL',
    'Password Changed',
    'Your password has been changed successfully.'
  );

  return res.status(200).json({
    status: 'success',
    message: 'Password changed successfully',
  });
});

/**
 * Get user notifications
 */
export const getNotifications = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const isRead = req.query.read === 'true' ? true : req.query.read === 'false' ? false : undefined;

  const result = await getUserNotifications(userId, page, limit, isRead);

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Mark notification as read
 */
export const markNotificationRead = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user!.id;
  const notificationId = req.params.id;

  const notification = await markNotificationAsRead(notificationId, userId);

  return res.status(200).json({
    status: 'success',
    message: 'Notification marked as read',
    data: { notification },
  });
});

/**
 * Mark all notifications as read
 */
export const markAllNotificationsReadHandler = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  const result = await markAllNotificationsAsRead(userId);

  return res.status(200).json({
    status: 'success',
    message: `${result.count} notifications marked as read`,
    data: result,
  });
});
