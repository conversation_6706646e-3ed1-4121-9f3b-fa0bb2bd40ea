import { Request, Response, NextFunction } from 'express';
import prisma from '../config/database';
import { AppError, asyncHandler } from '../utils/errorHandler';
import { validateGST } from '../services/gst.service';
import { createNotification } from '../services/notification.service';
import { notifyUserRegistration } from '../services/adminNotification.service';
import {
  sendPhoneOTP,
  verifyPhoneOTP,
  verifyRiderToken
} from '../services/firebase.service';
import {
  sendEmailOTP,
  verifyEmailOTP
} from '../services/otp.service';
import bcrypt from 'bcrypt';
import { riderFirebaseApp } from '../config/firebase';

/**
 * Register a new rider
 */
export const registerRider = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const {
    gstNumber,
    companyName,
    contactPerson,
    contactNumber,
    email,
    firebaseUid,
    paymentTerms,
    password
  } = req.body;

  // Check if rider already exists
  const existingRider = await prisma.rider.findFirst({
    where: {
      OR: [
        { email },
        { gstNumber },
        { firebaseUid }
      ]
    }
  });

  if (existingRider) {
    if (existingRider.email === email) {
      return next(new AppError('Email already in use', 409));
    }
    if (existingRider.gstNumber === gstNumber) {
      return next(new AppError('GST number already registered', 409));
    }
    if (existingRider.firebaseUid === firebaseUid) {
      return next(new AppError('Firebase account already registered', 409));
    }
  }

  // Validate GST number
  const gstValidation = await validateGST(gstNumber);
  if (!gstValidation.valid) {
    return next(new AppError(gstValidation.error || 'Invalid GST number', 400));
  }

  // hash the password using jwt secret key 
  const hashedPassword = await bcrypt.hash(password, 10);

  // Create rider
  const rider = await prisma.rider.create({
    data: {
      gstNumber,
      companyName,
      contactPerson,
      contactNumber,
      email,
      firebaseUid,
      paymentTerms,
      password: hashedPassword
      // No password field as we're using Firebase auth
    }
  });

  // Create admin notification for new user registration using the new admin notification system
  try {
    await notifyUserRegistration({
      id: rider.id,
      companyName: rider.companyName,
      email: rider.email,
    });
  } catch (adminNotificationError) {
    console.error('Failed to create admin notification for user registration:', adminNotificationError);
    // Don't fail the main operation if admin notification fails
  }

  res.status(201).json({
    status: 'success',
    message: 'Registration successful. Your account is pending approval.',
    data: {
      rider: {
        id: rider.id,
        gstNumber: rider.gstNumber,
        companyName: rider.companyName,
        email: rider.email,
        isApproved: rider.isApproved
      }
    }
  });
});

/**
 * Register a rider with JWT token (after email verification)
 */
export const registerRiderWithJwt = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const {
    gstNumber,
    companyName,
    contactPerson,
    contactNumber,
    email,
    paymentTerms
  } = req.body;

  // Check if rider already exists
  const existingRider = await prisma.rider.findFirst({
    where: {
      OR: [
        { email },
        { gstNumber }
      ]
    }
  });

  if (existingRider) {
    if (existingRider.email === email) {
      return next(new AppError('Email already in use', 409));
    }
    if (existingRider.gstNumber === gstNumber) {
      return next(new AppError('GST number already registered', 409));
    }
  }

  // Validate GST number
  const gstValidation = await validateGST(gstNumber);
  if (!gstValidation.valid) {
    return next(new AppError(gstValidation.error || 'Invalid GST number', 400));
  }

  // Verify JWT token contains the same email
  if (!req.user || req.user.email !== email) {
    return next(new AppError('Email in request does not match authenticated user', 400));
  }

  // Create rider
  const rider = await prisma.rider.create({
    data: {
      gstNumber,
      companyName,
      contactPerson,
      contactNumber,
      email,
      paymentTerms,
      // No Firebase UID as we're using JWT
    }
  });

  // Create admin notification for new user registration using the new admin notification system
  try {
    await notifyUserRegistration({
      id: rider.id,
      companyName: rider.companyName,
      email: rider.email,
    });
  } catch (adminNotificationError) {
    console.error('Failed to create admin notification for user registration:', adminNotificationError);
    // Don't fail the main operation if admin notification fails
  }

  res.status(201).json({
    status: 'success',
    message: 'Registration successful. Your account is pending approval.',
    data: {
      rider: {
        id: rider.id,
        gstNumber: rider.gstNumber,
        companyName: rider.companyName,
        email: rider.email,
        isApproved: rider.isApproved
      }
    }
  });
});

/**
 * Login with Firebase token
 */
export const loginWithEmail = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { email, password } = req.body;

  // Verify email and password
  const rider = await prisma.rider.findUnique({
    where: { email },
    select: { id: true, isApproved: true, password: true, contactNumber: true, contactPerson: true, firebaseUid: true }
  });

  if (!rider) {
    return next(new AppError('Invalid email or password', 401));
  }

  // match password with hashed password
  const isPasswordValid = await bcrypt.compare(password, rider.password);
  if (!isPasswordValid) {
    return next(new AppError('Invalid email or password', 401));
  }

  // Create a custom token using Firebase Admin SDK
  try {
    const customToken = await riderFirebaseApp.auth().createCustomToken(rider.firebaseUid);
    
    res.status(200).json({
      status: 'success',
      message: 'Login successful',
      data: {
        rider: {
          id: rider.id,
          isApproved: rider.isApproved,
          customToken: customToken
        }
      }
    });
  } catch (error) {
    console.error('Error creating custom token:', error);
    return next(new AppError('Failed to generate Firebase token', 500));
  }
});

export const loginWithPhone = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const {  uid } = req.body;

  // Verify phone number and uid
  const rider = await prisma.rider.findFirst({
    where: { firebaseUid: uid },
    select: { id: true, isApproved: true, firebaseUid: true }
  });

  if (!rider) {
    return next(new AppError('Invalid phone number or uid', 401));
  }

  // Create a custom token using Firebase Admin SDK
  try {
    const customToken = await riderFirebaseApp.auth().createCustomToken(rider.firebaseUid);
    
    res.status(200).json({
      status: 'success',
      message: 'Login successful',
      data: {
        rider: {
          id: rider.id,
          isApproved: rider.isApproved,
          customToken: customToken,
          user : rider
        }
      }
    });
  } catch (error) {
    console.error('Error creating custom token:', error);
    return next(new AppError('Failed to generate Firebase token', 500));
  }
}); 

/**
 * Send phone OTP
 */
export const sendPhoneOTPHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  let { phoneNumber, recaptchaToken } = req.body;
  //format phone number with country code if not present
  if (!phoneNumber.startsWith('+')) {
    phoneNumber = `+91${phoneNumber}`;
  }

  const result = await sendPhoneOTP(phoneNumber, recaptchaToken);

  res.status(200).json({
    status: 'success',
    message: 'OTP sent successfully',
    data: {
      verificationId: result.verificationId,
      expiresIn: 600 // 10 minutes
    }
  });
});

/**
 * Verify phone OTP
 */
export const verifyPhoneOTPHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { verificationId, code } = req.body;

  const result = await verifyPhoneOTP(verificationId, code);

  if (!result.success) {
    return next(new AppError('Invalid OTP', 400));
  }

  // Return Firebase ID token to the client for authentication
  res.status(200).json({
    status: 'success',
    message: 'Phone verification successful',
    data: {
      firebaseToken: result.idToken,
      firebaseUid: result.uid
    }
  });
});

/**
 * Send email OTP
 */
export const sendEmailOTPHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { email } = req.body;

  const result = await sendEmailOTP(email);

  res.status(200).json({
    status: 'success',
    message: 'OTP sent successfully to your email',
    data: {
      expiresIn: result.expiresIn // 10 minutes
    }
  });
});

/**
 * Verify email OTP
 */
export const verifyEmailOTPHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { email, otp } = req.body;

  const result = await verifyEmailOTP(email, otp);

  if (!result.success) {
    return next(new AppError('Invalid OTP', 400));
  }

  // Return JWT token to the client for authentication
  res.status(200).json({
    status: 'success',
    message: 'Email verification successful',
    data: {
      token: result.token,
      user: result.user
    }
  });
});
