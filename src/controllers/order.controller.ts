import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getUserOrders,
  getOrderById,
  createOrder,
  cancelOrder,
  updateOrderStatus,
  updatePaymentStatus,
  getAllOrders,
} from '../services/order.service';
import { OrderStatus, PaymentStatus } from '@prisma/client';

/**
 * Get user's orders
 */
export const getOrders = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const {
    page,
    limit,
    search,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getUserOrders(userId, {
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    search: search as string,
    status: status as OrderStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get all orders (admin only)
 */
export const getAllOrdersHandler = asyncHandler(async (req: Request, res: Response) => {
  const {
    page,
    limit,
    search,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getAllOrders({
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    search: search as string,
    status: status as OrderStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });
  console.log("result of get all orders is : ", result);

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get order by ID
 */
export const getOrder = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const isAdmin = req.user!.role === 'ADMIN';

  // If user is admin, don't check ownership
  const order = await getOrderById(id, isAdmin ? undefined : userId);

  return res.status(200).json({
    status: 'success',
    data: { order },
  });
});

/**
 * Get order details by ID (admin only)
 */
export const getOrderDetailsHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const order = await getOrderById(id);

  if (!order) {
    return next(new AppError('Order not found', 404));
  }

  return res.status(200).json({
    status: 'success',
    data: { order },
  });
});

/**
 * Create new order from cart
 */
export const createOrderHandler = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const { fulfillmentType, shippingAddress, vehicleNumber, notes } = req.body;

  const order = await createOrder(userId, {
    fulfillmentType,
    shippingAddress,
    vehicleNumber,
    notes,
  });

  return res.status(201).json({
    status: 'success',
    message: 'Order created successfully',
    data: { order },
  });
});

/**
 * Cancel an order
 */
export const cancelOrderHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const order = await cancelOrder(id, userId);

  return res.status(200).json({
    status: 'success',
    message: 'Order cancelled successfully',
    data: { order },
  });
});

/**
 * Update order status (admin only)
 */
export const updateOrderStatusHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status } = req.body;

  const order = await updateOrderStatus(id, status);

  return res.status(200).json({
    status: 'success',
    message: `Order status updated to ${status}`,
    data: { order },
  });
});

/**
 * Update payment status (admin only)
 */
export const updatePaymentStatusHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { paymentStatus } = req.body;

  const order = await updatePaymentStatus(id, paymentStatus);

  return res.status(200).json({
    status: 'success',
    message: `Payment status updated to ${paymentStatus}`,
    data: { order },
  });
});
