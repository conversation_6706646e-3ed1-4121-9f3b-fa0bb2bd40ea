import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import { verifyAdminToken } from '../services/firebase.service';

/**
 * Admin login with Firebase token
 */
export const loginAdminWithFirebase = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { firebaseToken } = req.body;

  try {
    // Verify Firebase token
    const { uid, email } = await verifyAdminToken(firebaseToken);

    res.status(200).json({
      status: 'success',
      message: 'Admin login successful',
      data: {
        admin: {
          id: uid,
          email,
          role: 'ADMIN'
        },
        token: firebaseToken // Return the same token for client to store
      }
    });
  } catch (error) {
    return next(new AppError('Invalid or expired admin token', 401));
  }
});

/**
 * Get admin profile
 */
export const getAdminProfile = asyncHandler(async (req: Request, res: Response) => {
  res.status(200).json({
    status: 'success',
    data: {
      admin: req.user
    }
  });
});
