import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getUserEnquiries,
  getEnquiryById,
  createEnquiry,
  closeEnquiry,
  respondToEnquiry,
  getAllEnquiries,
} from '../services/enquiry.service';
import { EnquiryStatus } from '@prisma/client';

/**
 * Get user's enquiries
 */
export const getEnquiries = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const {
    page,
    limit,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getUserEnquiries(userId, {
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    status: status as EnquiryStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get all enquiries (admin only)
 */
export const getAllEnquiriesHandler = asyncHandler(async (req: Request, res: Response) => {
  const {
    page,
    limit,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getAllEnquiries({
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    status: status as EnquiryStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get enquiry by ID
 */
export const getEnquiry = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const isAdmin = req.user!.role === 'ADMIN';

  // If user is admin, don't check ownership
  const enquiry = await getEnquiryById(id, isAdmin ? undefined : userId);

  return res.status(200).json({
    status: 'success',
    data: { enquiry },
  });
});

/**
 * Create new enquiry
 */
export const createEnquiryHandler = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const { type, gsm, bf, width, quantity, message, contactPreference } = req.body;

  const enquiry = await createEnquiry(userId, {
    type,
    gsm,
    bf,
    width,
    quantity,
    message,
    contactPreference,
  });

  return res.status(201).json({
    status: 'success',
    message: 'Enquiry created successfully',
    data: { enquiry },
  });
});

/**
 * Close an enquiry
 */
export const closeEnquiryHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const enquiry = await closeEnquiry(id, userId);

  return res.status(200).json({
    status: 'success',
    message: 'Enquiry closed successfully',
    data: { enquiry },
  });
});

/**
 * Respond to an enquiry (admin only)
 */
export const respondToEnquiryHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { response } = req.body;

  const enquiry = await respondToEnquiry(id, response);

  return res.status(200).json({
    status: 'success',
    message: 'Response sent successfully',
    data: { enquiry },
  });
});
