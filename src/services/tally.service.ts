import axios from 'axios';
import * as xml2js from 'xml2js';
import { AppError } from '../utils/errorHandler';
import redisClient from '../config/redis';
import prisma from '../config/database';

const parseStringPromise = xml2js.parseStringPromise;

// Cache TTL in seconds (1 hour)
const CACHE_TTL = 3600;

// Tally API configuration
const TALLY_API_URL = process.env.TALLY_API_URL || 'http://localhost:9000';

/**
 * Get customer ledger from Tally
 * @param gstNumber - Customer GST number
 * @returns Customer ledger data
 */
export const getCustomerLedger = async (gstNumber: string) => {
  try {
    // Create cache key
    const cacheKey = `tally:ledger:${gstNumber}`;

    // Try to get from cache
    const cachedData = await redisClient.get(cacheKey);
    if (cachedData) {
      return JSON.parse(cachedData.toString());
    }

    // Check if user exists
    const user = await prisma.rider.findUnique({
      where: { gstNumber },
      select: { id: true, companyName: true },
    });

    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Create XML request for Tally
    const requestXML = `
      <ENVELOPE>
        <HEADER>
          <VERSION>1</VERSION>
          <TALLYREQUEST>Export</TALLYREQUEST>
          <TYPE>Data</TYPE>
          <ID>LedgerBalance</ID>
        </HEADER>
        <BODY>
          <DESC>
            <STATICVARIABLES>
              <SVCURRENTCOMPANY>Kraft Paper Company</SVCURRENTCOMPANY>
              <LEDGERNAME>${user.companyName}</LEDGERNAME>
            </STATICVARIABLES>
          </DESC>
        </BODY>
      </ENVELOPE>
    `;

    // Make request to Tally
    const response = await axios.post(TALLY_API_URL, requestXML, {
      headers: {
        'Content-Type': 'text/xml',
      },
    });

    // Parse XML response
    const parsedResponse = await parseStringPromise(response.data);

    // Extract ledger data
    const ledgerData = {
      companyName: user.companyName,
      gstNumber,
      openingBalance: parsedResponse?.ENVELOPE?.BODY?.[0]?.DATA?.[0]?.OPENINGBALANCE?.[0] || '0',
      closingBalance: parsedResponse?.ENVELOPE?.BODY?.[0]?.DATA?.[0]?.CLOSINGBALANCE?.[0] || '0',
      creditLimit: parsedResponse?.ENVELOPE?.BODY?.[0]?.DATA?.[0]?.CREDITLIMIT?.[0] || '0',
      creditPeriod: parsedResponse?.ENVELOPE?.BODY?.[0]?.DATA?.[0]?.CREDITPERIOD?.[0] || '0',
    };

    // Cache the result
    await redisClient.set(cacheKey, JSON.stringify(ledgerData), {
      EX: CACHE_TTL,
    });

    return ledgerData;
  } catch (error: any) {
    // If Tally is not available, return mock data
    if (error.code === 'ECONNREFUSED' || error.response?.status === 404) {
      console.warn('Tally API not available, returning mock data');

      // Create mock ledger data
      const mockLedgerData = {
        companyName: (await prisma.rider.findUnique({
          where: { gstNumber },
          select: { companyName: true },
        }))?.companyName || 'Unknown',
        gstNumber,
        openingBalance: '50000.00',
        closingBalance: '75000.00',
        creditLimit: '100000.00',
        creditPeriod: '30',
        isMockData: true,
      };

      return mockLedgerData;
    }

    // For other errors, throw the error
    throw error;
  }
};

/**
 * Sync invoice with Tally
 * @param orderId - Order ID
 * @returns Sync status
 */
export const syncInvoiceWithTally = async (orderId: string) => {
  try {
    // Get order details
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        rider: {
          select: {
            companyName: true,
            gstNumber: true,
          },
        },
        items: true,
      },
    });

    if (!order) {
      throw new AppError('Order not found', 404);
    }

    // Create XML request for Tally
    const requestXML = `
      <ENVELOPE>
        <HEADER>
          <VERSION>1</VERSION>
          <TALLYREQUEST>Import</TALLYREQUEST>
          <TYPE>Data</TYPE>
          <ID>Vouchers</ID>
        </HEADER>
        <BODY>
          <DESC>
            <STATICVARIABLES>
              <SVCURRENTCOMPANY>Kraft Paper Company</SVCURRENTCOMPANY>
            </STATICVARIABLES>
          </DESC>
          <DATA>
            <TALLYMESSAGE>
              <VOUCHER>
                <DATE>${formatDateForTally(order.createdAt)}</DATE>
                <VOUCHERTYPENAME>Sales</VOUCHERTYPENAME>
                <VOUCHERNUMBER>${order.orderNumber}</VOUCHERNUMBER>
                <REFERENCE>${order.id}</REFERENCE>
                <PARTYLEDGERNAME>${order.rider.companyName}</PARTYLEDGERNAME>
                <PARTYGSTIN>${order.rider.gstNumber}</PARTYGSTIN>
                <AMOUNT>${order.totalAmount}</AMOUNT>
                <NARRATION>Order ${order.orderNumber}</NARRATION>
                <ALLLEDGERENTRIES.LIST>
                  <LEDGERNAME>${order.rider.companyName}</LEDGERNAME>
                  <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                  <AMOUNT>-${order.totalAmount}</AMOUNT>
                </ALLLEDGERENTRIES.LIST>
                <ALLLEDGERENTRIES.LIST>
                  <LEDGERNAME>Sales</LEDGERNAME>
                  <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
                  <AMOUNT>${order.totalAmount}</AMOUNT>
                </ALLLEDGERENTRIES.LIST>
              </VOUCHER>
            </TALLYMESSAGE>
          </DATA>
        </BODY>
      </ENVELOPE>
    `;

    // Make request to Tally
    const response = await axios.post(TALLY_API_URL, requestXML, {
      headers: {
        'Content-Type': 'text/xml',
      },
    });

    // Parse XML response
    const parsedResponse = await parseStringPromise(response.data);

    // Check if sync was successful
    const isSuccess = parsedResponse?.ENVELOPE?.BODY?.[0]?.DATA?.[0]?.LINEERROR?.[0] !== 'True';

    if (!isSuccess) {
      throw new AppError('Failed to sync invoice with Tally', 500);
    }

    return {
      success: true,
      message: 'Invoice synced with Tally successfully',
      orderId,
      orderNumber: order.orderNumber,
    };
  } catch (error: any) {
    // If Tally is not available, return mock success
    if (error.code === 'ECONNREFUSED' || error.response?.status === 404) {
      console.warn('Tally API not available, returning mock success');

      return {
        success: true,
        message: 'Invoice synced with Tally successfully (mock)',
        orderId,
        isMockData: true,
      };
    }

    // For other errors, throw the error
    throw error;
  }
};

/**
 * Format date for Tally (YYYYMMDD)
 * @param date - Date to format
 * @returns Formatted date string
 */
const formatDateForTally = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}${month}${day}`;
};
