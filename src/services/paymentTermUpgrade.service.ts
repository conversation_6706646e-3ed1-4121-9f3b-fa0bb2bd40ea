import prisma from '../config/database';
import { AppError } from '../utils/errorHandler';
import { PaymentTermUpgradeRequest, RequestStatus, PaymentTerms } from '@prisma/client';
import { createNotification } from './notification.service';
import { notifyPaymentTermUpgradeRequest } from './adminNotification.service';

/**
 * Get user's payment term upgrade requests
 * @param userId - User ID
 * @param options - Query options for filtering and pagination
 * @returns Paginated payment term upgrade requests
 */
export const getUserPaymentTermUpgradeRequests = async (
  userId: string,
  options: {
    page?: number;
    limit?: number;
    status?: RequestStatus;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = { userId };
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get requests with pagination
  const [requests, total] = await Promise.all([
    prisma.paymentTermUpgradeRequest.findMany({
      where,
      orderBy,
      skip,
      take: limit,
    }),
    prisma.paymentTermUpgradeRequest.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    requests,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get all payment term upgrade requests (admin only)
 * @param options - Query options for filtering and pagination
 * @returns Paginated payment term upgrade requests with user details
 */
export const getAllPaymentTermUpgradeRequests = async (options: {
  page?: number;
  limit?: number;
  status?: RequestStatus;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get requests with pagination
  const [requests, total] = await Promise.all([
    prisma.paymentTermUpgradeRequest.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        rider: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true,
            email: true,
            contactNumber: true,
          },
        },
      },
    }),
    prisma.paymentTermUpgradeRequest.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    requests,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get payment term upgrade request by ID
 * @param requestId - Request ID
 * @param userId - User ID (optional, for authorization)
 * @returns Payment term upgrade request
 */
export const getPaymentTermUpgradeRequestById = async (
  requestId: string,
  userId?: string
): Promise<PaymentTermUpgradeRequest> => {
  const where: any = { id: requestId };
  if (userId) where.userId = userId;

  const request = await prisma.paymentTermUpgradeRequest.findFirst({
    where,
    include: {
      rider: {
        select: {
          id: true,
          companyName: true,
          contactPerson: true,
          email: true,
          contactNumber: true,
        },
      },
    },
  });

  if (!request) {
    throw new AppError('Payment term upgrade request not found', 404);
  }

  return request;
};

/**
 * Create new payment term upgrade request
 * @param userId - User ID
 * @param data - Request data
 * @returns Created payment term upgrade request
 */
export const createPaymentTermUpgradeRequest = async (
  userId: string,
  data: {
    reason: string;
    requestedPaymentTerms: PaymentTerms;
  }
): Promise<PaymentTermUpgradeRequest> => {
  // Get user's current payment terms
  const user = await prisma.rider.findUnique({
    where: { id: userId },
    select: { paymentTerms: true },
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Check if user already has a pending request
  const existingRequest = await prisma.paymentTermUpgradeRequest.findFirst({
    where: {
      userId,
      status: RequestStatus.PENDING,
    },
  });

  if (existingRequest) {
    throw new AppError('You already have a pending payment term upgrade request', 400);
  }

  // Validate that requested terms are higher than current terms
  const termHierarchy = {
    [PaymentTerms.IMMEDIATE]: 0,
    [PaymentTerms.THIRTY_DAYS]: 1,
    [PaymentTerms.SIXTY_DAYS]: 2,
  };

  if (termHierarchy[data.requestedPaymentTerms] <= termHierarchy[user.paymentTerms]) {
    throw new AppError('Requested payment terms must be higher than current terms', 400);
  }

  // Create request
  const request = await prisma.paymentTermUpgradeRequest.create({
    data: {
      userId,
      reason: data.reason,
      currentPaymentTerms: user.paymentTerms,
      requestedPaymentTerms: data.requestedPaymentTerms,
    },
  });

  // Create notification for user
  await createNotification(
    userId,
    'ACCOUNT_APPROVAL',
    'Payment Term Upgrade Request Submitted',
    `Your payment term upgrade request has been submitted and is pending review.`
  );

  // Create notification for admin
  const userDetails = await prisma.rider.findUnique({
    where: { id: userId },
    select: { companyName: true },
  });

  await notifyPaymentTermUpgradeRequest({
    id: request.id,
    userId: request.userId,
    companyName: userDetails?.companyName || 'Unknown Company',
    currentPaymentTerms: request.currentPaymentTerms,
    requestedPaymentTerms: request.requestedPaymentTerms,
  });

  return request;
};

/**
 * Update payment term upgrade request status (admin only)
 * @param requestId - Request ID
 * @param status - New status
 * @returns Updated request
 */
export const updatePaymentTermUpgradeRequestStatus = async (
  requestId: string,
  status: RequestStatus
): Promise<PaymentTermUpgradeRequest> => {
  // Get request
  const request = await getPaymentTermUpgradeRequestById(requestId);

  // Check if request is still pending
  if (request.status !== RequestStatus.PENDING) {
    throw new AppError('Only pending requests can be updated', 400);
  }

  // Update request status
  const updatedRequest = await prisma.paymentTermUpgradeRequest.update({
    where: { id: requestId },
    data: { status },
  });

  // If approved, update user's payment terms
  if (status === RequestStatus.APPROVED) {
    await prisma.rider.update({
      where: { id: request.userId },
      data: { paymentTerms: request.requestedPaymentTerms },
    });
  }

  // Create notification for user
  const notificationTitle = status === RequestStatus.APPROVED 
    ? 'Payment Term Upgrade Approved' 
    : 'Payment Term Upgrade Rejected';
  
  const notificationMessage = status === RequestStatus.APPROVED
    ? `Your payment term upgrade request has been approved. Your new payment terms are ${request.requestedPaymentTerms}.`
    : `Your payment term upgrade request has been rejected.`;

  await createNotification(
    request.userId,
    'ACCOUNT_APPROVAL',
    notificationTitle,
    notificationMessage
  );

  return updatedRequest;
};
