import axios from 'axios';
import crypto from 'crypto';
import { riderFirebaseApp, adminFirebaseApp } from '../config/firebase';
import { sendOTPEmail } from './email.service';

// Define interfaces for the OTP data structure
interface OTPData {
  sessionInfo: string;
  expiresAt: number;
}

interface EmailOTPData {
  email: string;
  otp: string;
  expiresAt: number;
  attempts: number;
}

// Define response interfaces
interface FirebaseSendOTPResponse {
  sessionInfo: string;
}

interface FirebaseVerifyOTPResponse {
  idToken?: string;
}

interface SendPhoneOTPResult {
  verificationId: string;
}

interface VerifyPhoneOTPResult {
  success: boolean;
  idToken: string;
  uid: string;
}

interface SendEmailOTPResult {
  success: boolean;
  expiresIn: number;
}

interface VerifyEmailOTPResult {
  success: boolean;
  token: string;
  uid: string;
}

// In-memory store for session info (use a database in production)
const otpStore = new Map<string, OTPData>();
const emailOtpStore = new Map<string, EmailOTPData>();

// Rider Firebase Authentication
// Verify rider Firebase ID token
export const verifyRiderToken = async (idToken: string): Promise<{uid: string, email: string}> => {
  try {
    const decodedToken = await riderFirebaseApp.auth().verifyIdToken(idToken);
    return {
      uid: decodedToken.uid,
      email: decodedToken.email || ''
    };
  } catch (error) {
    console.error('Error verifying rider token:', error);
    throw new Error('Invalid or expired rider token');
  }
};

// Admin Firebase Authentication
// Verify admin Firebase ID token
export const verifyAdminToken = async (idToken: string): Promise<{uid: string, email: string}> => {
  try {
    const decodedToken = await adminFirebaseApp.auth().verifyIdToken(idToken);
  
    
    return {
      uid: decodedToken.uid,
      email: decodedToken.email || ''
    };
  } catch (error) {
    console.error('Error verifying admin token:', error);
    throw new Error('Invalid or expired admin token');
  }
};

// Send SMS with OTP
export const sendPhoneOTP = async (phoneNumber: string, recaptchaToken: string): Promise<SendPhoneOTPResult> => {
  try {
    // Validate phone number format
    if (!phoneNumber.startsWith('+')) {
      throw new Error('Phone number must include country code (e.g., +91)');
    }

    // Request Firebase to send SMS
    const response = await axios.post<FirebaseSendOTPResponse>(
      `https://www.googleapis.com/identitytoolkit/v3/relyingparty/sendVerificationCode?key=${process.env.RIDER_FIREBASE_API_KEY}`,
      {
        phoneNumber,
        recaptchaToken,
      }
    );

    const sessionInfo = response.data.sessionInfo;
    const verificationId = crypto.randomBytes(16).toString('hex');

    // Store session info with expiration (10 minutes)
    otpStore.set(verificationId, { sessionInfo, expiresAt: Date.now() + 10 * 60 * 1000 });

    return { verificationId };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error sending phone OTP:', error.response ? error.response.data : error.message);
    } else {
      console.error('Error sending phone OTP:', error);
    }
    throw new Error('Failed to send OTP to phone number');
  }
};

// Verify OTP
export const verifyPhoneOTP = async (verificationId: string, code: string): Promise<VerifyPhoneOTPResult> => {
  try {
    const otpData = otpStore.get(verificationId);
    if (!otpData) {
      throw new Error('Invalid or expired verification ID');
    }

    if (Date.now() > otpData.expiresAt) {
      otpStore.delete(verificationId);
      throw new Error('OTP has expired. Please request a new one.');
    }

    // Verify the OTP with Firebase
    const response = await axios.post<FirebaseVerifyOTPResponse>(
      `https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyPhoneNumber?key=${process.env.RIDER_FIREBASE_API_KEY}`,
      {
        sessionInfo: otpData.sessionInfo,
        code,
      }
    );

    if (response.data.idToken) {
      // Decode the idToken to get the user ID
      const decoded = await riderFirebaseApp.auth().verifyIdToken(response.data.idToken);
      
      otpStore.delete(verificationId);
      return { 
        success: true, 
        idToken: response.data.idToken,
        uid: decoded.uid
      };
    } else {
      throw new Error('Invalid OTP');
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error verifying phone OTP:', error.response ? error.response.data : error.message);
    } else {
      console.error('Error verifying phone OTP:', error);
    }
    throw new Error('Failed to verify OTP');
  }
};

// Generate a 6-digit OTP code
const generateOTP = (): string => {
  // Generate a random 6-digit number
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send Email OTP
export const sendEmailOTP = async (email: string): Promise<{success: boolean; expiresIn: number}> => {
  try {
    // Generate a new OTP
    const otp = generateOTP();
    
    // Store OTP with expiration (10 minutes)
    const expiresAt = Date.now() + 10 * 60 * 1000;
    
    // Send the OTP via email
    await sendOTPEmail(email, otp);
    
    // Store in our map
    emailOtpStore.set(email, { 
      email,
      otp,
      expiresAt,
      attempts: 1
    });
    
    return { 
      success: true,
      expiresIn: 10 * 60 // 10 minutes in seconds
    };
  } catch (error) {
    console.error('Error sending email OTP:', error);
    throw new Error('Failed to send OTP to email');
  }
};

// Verify Email OTP
export const verifyEmailOTP = async (email: string, otp: string): Promise<{success: boolean; idToken: string; uid: string}> => {
  try {
    const otpData = emailOtpStore.get(email);
    
    if (!otpData) {
      throw new Error('No OTP found for this email. Please request a new OTP.');
    }
    
    if (Date.now() > otpData.expiresAt) {
      emailOtpStore.delete(email);
      throw new Error('OTP has expired. Please request a new one.');
    }
    
    if (otpData.otp !== otp) {
      throw new Error('Invalid OTP. Please try again.');
    }
    
    // OTP is valid, create a custom token for this user or sign them in
    // First check if user exists in Firebase
    try {
      const userRecord = await riderFirebaseApp.auth().getUserByEmail(email);
      
      // User exists, create a custom token
      const customToken = await riderFirebaseApp.auth().createCustomToken(userRecord.uid);
      
      // Exchange custom token for ID token
      const response = await axios.post(
        `https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=${process.env.RIDER_FIREBASE_API_KEY}`,
        {
          token: customToken,
          returnSecureToken: true
        }
      );
      
      // Clean up
      emailOtpStore.delete(email);
      
      return {
        success: true,
        idToken: response.data.idToken,
        uid: userRecord.uid
      };
    } catch (error) {
      // User doesn't exist, create a new one
      const userRecord = await riderFirebaseApp.auth().createUser({
        email: email,
        emailVerified: true, // Since we just verified via OTP
      });
      
      // Create custom token for the new user
      const customToken = await riderFirebaseApp.auth().createCustomToken(userRecord.uid);
      
      // Exchange custom token for ID token
      const response = await axios.post(
        `https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=${process.env.RIDER_FIREBASE_API_KEY}`,
        {
          token: customToken,
          returnSecureToken: true
        }
      );
      
      // Clean up
      emailOtpStore.delete(email);
      
      return {
        success: true,
        idToken: response.data.idToken,
        uid: userRecord.uid
      };
    }
  } catch (error) {
    console.error('Error verifying email OTP:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to verify OTP');
  }
};
