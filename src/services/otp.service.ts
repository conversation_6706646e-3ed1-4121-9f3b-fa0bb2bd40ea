import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { sendOTPEmail } from './email.service';
import prisma from '../config/database';

// Interface for email OTP data
interface EmailOTPData {
  email: string;
  otp: string;
  expiresAt: number;
  attempts: number;
}

// In-memory store for OTPs (in production, consider using Redis)
const emailOtpStore = new Map<string, EmailOTPData>();

// Secret for JWT token
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = '7d';

/**
 * Generate a 6-digit OTP code
 */
const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Send OTP to email
 */
export const sendEmailOTP = async (email: string): Promise<{ success: boolean; expiresIn: number }> => {
  try {
    // Generate a new OTP
    const otp = generateOTP();
    
    // Store OTP with expiration (10 minutes)
    const expiresAt = Date.now() + 10 * 60 * 1000;
    
    // Send the OTP via email
    await sendOTPEmail(email, otp);
    
    // Store in our map
    emailOtpStore.set(email, { 
      email,
      otp,
      expiresAt,
      attempts: 0
    });
    
    return { 
      success: true,
      expiresIn: 10 * 60 // 10 minutes in seconds
    };
  } catch (error) {
    console.error('Error sending email OTP:', error);
    throw new Error('Failed to send OTP to email');
  }
};

/**
 * Verify OTP from email
 */
export const verifyEmailOTP = async (email: string, otp: string): Promise<{ 
  success: boolean; 
  token: string;
  user: {
    id: number | null;
    email: string;
    isNewUser: boolean;
  }
}> => {
  try {
    const otpData = emailOtpStore.get(email);
    
    if (!otpData) {
      throw new Error('No OTP found for this email. Please request a new OTP.');
    }
    
    if (Date.now() > otpData.expiresAt) {
      emailOtpStore.delete(email);
      throw new Error('OTP has expired. Please request a new one.');
    }
    
    // Increment attempts
    otpData.attempts += 1;
    
    // Check if OTP is correct
    if (otpData.otp !== otp) {
      // If too many failed attempts, delete the OTP
      if (otpData.attempts >= 3) {
        emailOtpStore.delete(email);
        throw new Error('Too many failed attempts. Please request a new OTP.');
      }
      
      throw new Error('Invalid OTP. Please try again.');
    }
    
    // Check if user exists in database
    const existingUser = await prisma.rider.findUnique({
      where: { email },
      select: { id: true, email: true }
    });
    
    // Create JWT token
    const token = jwt.sign(
      { 
        email,
        id: existingUser?.id || null,
        isVerified: true
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    // Clean up
    // emailOtpStore.delete(email);
    
    return {
      success: true,
      token,
      user: {
        id: existingUser?.id ? Number(existingUser.id) : null,
        email,
        isNewUser: !existingUser
      }
    };
  } catch (error) {
    console.error('Error verifying email OTP:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to verify OTP');
  }
};
