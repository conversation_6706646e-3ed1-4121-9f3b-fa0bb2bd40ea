import { PaymentTerms, Stock } from '@prisma/client';

/**
 * Get the appropriate price for a user based on their payment terms
 * @param stock - Stock item with all price tiers
 * @param paymentTerms - User's payment terms
 * @returns The appropriate price for the user
 */
export const getPriceForPaymentTerms = (stock: Stock, paymentTerms: PaymentTerms): number => {
  switch (paymentTerms) {
    case PaymentTerms.THIRTY_DAYS:
      return stock.thirtyDayPrice;
    case PaymentTerms.SIXTY_DAYS:
      return stock.sixtyDayPrice;
    case PaymentTerms.IMMEDIATE:
    default:
      return stock.immediatePrice;
  }
};

/**
 * Filter stock items to only include the appropriate price for the user
 * @param stocks - Array of stock items
 * @param paymentTerms - User's payment terms
 * @returns Stock items with filtered pricing
 */
export const filterStockPrices = (stocks: Stock[], paymentTerms: PaymentTerms): any[] => {
  return stocks.map(stock => {
    const price = getPriceForPaymentTerms(stock, paymentTerms);
    
    return {
      id: stock.id,
      type: stock.type,
      gsm: stock.gsm,
      bf: stock.bf,
      width: stock.width,
      rollsAvailable: stock.rollsAvailable,
      pricePerRoll: price, // Single price based on payment terms
      createdAt: stock.createdAt,
      updatedAt: stock.updatedAt,
    };
  });
};

/**
 * Filter a single stock item to only include the appropriate price for the user
 * @param stock - Stock item
 * @param paymentTerms - User's payment terms
 * @returns Stock item with filtered pricing
 */
export const filterSingleStockPrice = (stock: Stock, paymentTerms: PaymentTerms): any => {
  const price = getPriceForPaymentTerms(stock, paymentTerms);
  
  return {
    id: stock.id,
    type: stock.type,
    gsm: stock.gsm,
    bf: stock.bf,
    width: stock.width,
    rollsAvailable: stock.rollsAvailable,
    pricePerRoll: price, // Single price based on payment terms
    createdAt: stock.createdAt,
    updatedAt: stock.updatedAt,
  };
};

/**
 * Calculate cart total based on user's payment terms
 * @param cartItems - Cart items with stock details
 * @param paymentTerms - User's payment terms
 * @returns Total cart value
 */
export const calculateCartTotal = (cartItems: any[], paymentTerms: PaymentTerms): number => {
  return cartItems.reduce((sum, item) => {
    if (!item.stock) return sum;
    
    const price = getPriceForPaymentTerms(item.stock, paymentTerms);
    return sum + (item.quantity * price);
  }, 0);
};
