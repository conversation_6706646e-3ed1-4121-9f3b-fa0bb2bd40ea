import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to log request and response details
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  // Store original send method
  const originalSend = res.send;

  // Get current timestamp
  const startTime = new Date();

  // Log request details
  console.log(`\n[${startTime.toISOString()}] ${req.method} ${req.originalUrl}`);

  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Request Body:', JSON.stringify(req.body, null, 2).substring(0, 200) + (JSON.stringify(req.body).length > 200 ? '...' : ''));
  }

  if (req.query && Object.keys(req.query).length > 0) {
    console.log('Query Params:', req.query);
  }

  if (req.params && Object.keys(req.params).length > 0) {
    console.log('URL Params:', req.params);
  }

  // Override send method to log response
  res.send = function (body: any): Response {
    // Calculate response time
    const endTime = new Date();
    const responseTime = endTime.getTime() - startTime.getTime();

    // Log response details
    console.log(`[${endTime.toISOString()}] Response ${res.statusCode} (${responseTime}ms)`);

    // Log response body for non-binary responses
    if (typeof body === 'string' && body.length < 500) {
      try {
        // Try to parse as JSON
        const parsedBody = JSON.parse(body);
        console.log('Response Body:', JSON.stringify(parsedBody, null, 2).substring(0, 200) + (body.length > 200 ? '...' : ''));
      } catch (e) {
        // Not JSON, log as string
        console.log('Response Body:', body.substring(0, 200) + (body.length > 200 ? '...' : ''));
      }
    } else if (body && typeof body === 'object') {
      console.log('Response Body:', JSON.stringify(body, null, 2).substring(0, 200) + (JSON.stringify(body).length > 200 ? '...' : ''));
    }

    console.log(''); // Empty line for better readability

    // Call original send
    return originalSend.call(this, body);
  };

  next();
};
