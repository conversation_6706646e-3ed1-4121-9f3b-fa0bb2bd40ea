version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=${PORT:-5000}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
      
      # Firebase Rider
      - RIDER_FIREBASE_PROJECT_ID=${RIDER_FIREBASE_PROJECT_ID}
      - RIDER_FIREBASE_PRIVATE_KEY=${RIDER_FIREBASE_PRIVATE_KEY}
      - RIDER_FIREBASE_CLIENT_EMAIL=${RIDER_FIREBASE_CLIENT_EMAIL}
      - RIDER_FIREBASE_API_KEY=${RIDER_FIREBASE_API_KEY}
      
      # Firebase Admin
      - ADMIN_FIREBASE_PROJECT_ID=${ADMIN_FIREBASE_PROJECT_ID}
      - ADMIN_FIREBASE_PRIVATE_KEY=${ADMIN_FIREBASE_PRIVATE_KEY}
      - ADMIN_FIREBASE_CLIENT_EMAIL=${ADMIN_FIREBASE_CLIENT_EMAIL}
      - ADMIN_FIREBASE_API_KEY=${ADMIN_FIREBASE_API_KEY}
      
      # Email Configuration
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_SECURE=${SMTP_SECURE}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - SMTP_FROM=${SMTP_FROM}
      
      # Authentication & Security
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      
      # Other Configuration
      - FRONTEND_URL=${FRONTEND_URL}
      - DEFAULT_ADMIN_EMAIL=${DEFAULT_ADMIN_EMAIL}
      - GST_API_KEY=${GST_API_KEY}
    
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    
    depends_on:
      - redis
    
    restart: unless-stopped
    
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

volumes:
  redis_data:
    driver: local