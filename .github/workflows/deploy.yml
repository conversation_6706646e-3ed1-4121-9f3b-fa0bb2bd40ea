name: Deploy to EC2

on:
  push:
    branches: [ "main", "deve" ]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to EC2
    runs-on: self-hosted

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Create environment file
      run: |
        echo "🔧 Creating environment variables..."
        cat > .env << 'EOF'
        NODE_ENV=production
        PORT=5000
        DATABASE_URL=${{ secrets.PROD_DATABASE_URL }}
        REDIS_URL=${{ secrets.PROD_REDIS_URL || 'redis://localhost:6379' }}

        # Firebase configs
        RIDER_FIREBASE_PROJECT_ID=${{ secrets.RIDER_FIREBASE_PROJECT_ID }}
        RIDER_FIREBASE_PRIVATE_KEY="${{ secrets.RIDER_FIREBASE_PRIVATE_KEY }}"
        RIDER_FIREBASE_CLIENT_EMAIL=${{ secrets.RIDER_FIREBASE_CLIENT_EMAIL }}
        RIDER_FIREBASE_API_KEY=${{ secrets.RIDER_FIREBASE_API_KEY }}

        ADMIN_FIREBASE_PROJECT_ID=${{ secrets.ADMIN_FIREBASE_PROJECT_ID }}
        ADMIN_FIREBASE_PRIVATE_KEY="${{ secrets.ADMIN_FIREBASE_PRIVATE_KEY }}"
        ADMIN_FIREBASE_CLIENT_EMAIL=${{ secrets.ADMIN_FIREBASE_CLIENT_EMAIL }}
        ADMIN_FIREBASE_API_KEY=${{ secrets.ADMIN_FIREBASE_API_KEY }}

        # Email config
        SMTP_HOST=${{ secrets.SMTP_HOST }}
        SMTP_PORT=${{ secrets.SMTP_PORT }}
        SMTP_SECURE=${{ secrets.SMTP_SECURE }}
        SMTP_USER=${{ secrets.SMTP_USER }}
        SMTP_PASS=${{ secrets.SMTP_PASS }}
        SMTP_FROM=${{ secrets.SMTP_FROM }}

        # Other configs
        JWT_SECRET=${{ secrets.PROD_JWT_SECRET }}
        JWT_EXPIRES_IN=${{ secrets.PROD_JWT_EXPIRES_IN }}
        FRONTEND_URL=${{ secrets.PROD_FRONTEND_URL }}
        DEFAULT_ADMIN_EMAIL=${{ secrets.DEFAULT_ADMIN_EMAIL }}
        GST_API_KEY=${{ secrets.GST_API_KEY }}
        EOF

    - name: Install dependencies
      run: |
        echo "📦 Installing dependencies..."
        npm ci

    - name: Generate Prisma client
      run: |
        echo "🔧 Generating Prisma client..."
        npm run prisma:generate

    - name: Run database migrations
      run: |
        echo "🗄️ Running database migrations..."
        npm run prisma:deploy

    - name: Build application
      run: |
        echo "🔨 Building application..."
        npm run build

    - name: Install PM2 globally
      run: |
        echo "📦 Installing PM2..."
        npm install -g pm2

    - name: Create logs directory
      run: |
        echo "📁 Creating logs directory..."
        mkdir -p logs

    - name: Stop existing PM2 process
      run: |
        echo "🛑 Stopping existing kraft-server process..."
        pm2 stop kraft-server || true
        pm2 delete kraft-server || true

    - name: Start application with PM2
      run: |
        echo "🚀 Starting application with PM2..."
        pm2 start ecosystem.config.js

    - name: Configure PM2 startup
      run: |
        echo "⚙️ Configuring PM2 for auto-restart on reboot..."
        pm2 startup systemd -u $USER --hp $HOME || true
        pm2 save

    - name: Wait for application to start
      run: |
        echo "⏳ Waiting for application to start..."
        sleep 15

    - name: Health check
      run: |
        echo "🏥 Performing health check..."
        max_attempts=5
        attempt=1

        while [ $attempt -le $max_attempts ]; do
          if curl -f http://localhost:5000/health; then
            echo "✅ Health check passed! Application is running."
            break
          else
            echo "❌ Health check failed (attempt $attempt/$max_attempts)"
            if [ $attempt -eq $max_attempts ]; then
              echo "🔍 Checking PM2 logs..."
              pm2 logs kraft-server --lines 20
              exit 1
            fi
            sleep 10
            attempt=$((attempt + 1))
          fi
        done

    - name: Show PM2 status
      run: |
        echo "📊 PM2 process status:"
        pm2 status
        pm2 info kraft-server

    - name: Deployment complete
      run: |
        echo "🎉 Deployment completed successfully!"
        echo "Application is running on port 5000"
        echo "Health endpoint: http://localhost:5000/health"