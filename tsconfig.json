{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "noEmitOnError": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": false, "skipLibCheck": true, "outDir": "dist", "rootDir": ".", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "paths": {"@prisma/client": ["./generated/prisma"]}}, "include": ["**/*.ts", "app.ts"], "exclude": ["node_modules", "dist"]}