/*
  Warnings:

  - You are about to drop the column `subject` on the `Enquiry` table. All the data in the column will be lost.
  - Added the required column `bf` to the `Enquiry` table without a default value. This is not possible if the table is not empty.
  - Added the required column `gsm` to the `Enquiry` table without a default value. This is not possible if the table is not empty.
  - Added the required column `quantity` to the `Enquiry` table without a default value. This is not possible if the table is not empty.
  - Added the required column `type` to the `Enquiry` table without a default value. This is not possible if the table is not empty.
  - Added the required column `width` to the `Enquiry` table without a default value. This is not possible if the table is not empty.
  - Added the required column `width` to the `Stock` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "RequestStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "QueryStatus" AS ENUM ('PENDING', 'RESPONDED', 'CLOSED');

-- AlterEnum
ALTER TYPE "NotificationType" ADD VALUE 'PROMOTION';

-- AlterTable
ALTER TABLE "Enquiry" DROP COLUMN "subject",
ADD COLUMN     "bf" TEXT NOT NULL,
ADD COLUMN     "gsm" TEXT NOT NULL,
ADD COLUMN     "quantity" INTEGER NOT NULL,
ADD COLUMN     "type" TEXT NOT NULL,
ADD COLUMN     "width" TEXT NOT NULL,
ALTER COLUMN "message" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Stock" ADD COLUMN     "width" INTEGER NOT NULL;

-- CreateTable
CREATE TABLE "PaymentTermUpgradeRequest" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "currentPaymentTerms" "PaymentTerms" NOT NULL,
    "requestedPaymentTerms" "PaymentTerms" NOT NULL,
    "status" "RequestStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PaymentTermUpgradeRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SupportQuery" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "query" TEXT NOT NULL,
    "email" TEXT,
    "phone" TEXT,
    "name" TEXT,
    "status" "QueryStatus" NOT NULL DEFAULT 'PENDING',
    "response" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "respondedAt" TIMESTAMP(3),

    CONSTRAINT "SupportQuery_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PaymentTermUpgradeRequest" ADD CONSTRAINT "PaymentTermUpgradeRequest_userId_fkey" FOREIGN KEY ("userId") REFERENCES "Rider"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SupportQuery" ADD CONSTRAINT "SupportQuery_userId_fkey" FOREIGN KEY ("userId") REFERENCES "Rider"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
