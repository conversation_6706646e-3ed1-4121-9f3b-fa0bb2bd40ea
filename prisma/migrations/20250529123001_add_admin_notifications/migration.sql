-- CreateE<PERSON>
CREATE TYPE "AdminNotificationType" AS ENUM ('SYSTEM_ALERT', 'USER_REGISTRATION', 'ORDER_PLACED', 'ORDER_CANCELLED', 'PAYMENT_RECEIVED', '<PERSON><PERSON><PERSON><PERSON>_NOTIFICATION_SENT', 'STOCK_LOW', 'ENQUIRY_SUBMITTED', 'USER_APPROVED', 'USER_REJECTED', 'SYSTEM_ERROR');

-- CreateEnum
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- CreateEnum
CREATE TYPE "NotificationCategory" AS ENUM ('SYSTEM', 'USER_ACTION', 'ORDER', 'PAYMENT', 'STOCK', 'ENQUIRY');

-- CreateTable
CREATE TABLE "AdminNotification" (
    "id" TEXT NOT NULL,
    "type" "AdminNotificationType" NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "priority" "NotificationPriority" NOT NULL DEFAULT 'NORMAL',
    "category" "NotificationCategory" NOT NULL DEFAULT 'SYSTEM',
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AdminNotification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "AdminNotification_createdAt_idx" ON "AdminNotification"("createdAt");

-- CreateIndex
CREATE INDEX "AdminNotification_isRead_idx" ON "AdminNotification"("isRead");

-- CreateIndex
CREATE INDEX "AdminNotification_priority_idx" ON "AdminNotification"("priority");

-- CreateIndex
CREATE INDEX "AdminNotification_category_idx" ON "AdminNotification"("category");
