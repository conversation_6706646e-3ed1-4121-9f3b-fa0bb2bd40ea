/*
  Warnings:

  - A unique constraint covering the columns `[type,gsm,bf,width]` on the table `Stock` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `gstAmount` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `subtotalAmount` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `width` to the `OrderItem` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "Stock_type_gsm_bf_key";

-- AlterTable
ALTER TABLE "Order" ADD COLUMN     "gstAmount" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "subtotalAmount" DOUBLE PRECISION NOT NULL;

-- AlterTable
ALTER TABLE "OrderItem" ADD COLUMN     "width" INTEGER NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Stock_type_gsm_bf_width_key" ON "Stock"("type", "gsm", "bf", "width");
