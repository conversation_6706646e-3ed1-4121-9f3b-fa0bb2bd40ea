
# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/kraft

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Rider Firebase Configuration
RIDER_FIREBASE_PROJECT_ID=your-rider-firebase-project-id
RIDER_FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
RIDER_FIREBASE_CLIENT_EMAIL=your-rider-firebase-client-email
RIDER_FIREBASE_API_KEY=your-rider-firebase-api-key

# Admin Firebase Configuration
ADMIN_FIREBASE_PROJECT_ID=your-admin-firebase-project-id
ADMIN_FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
ADMIN_FIREBASE_CLIENT_EMAIL=your-admin-firebase-client-email
ADMIN_FIREBASE_API_KEY=your-admin-firebase-api-key


# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# GST Verification API (optional)
GST_API_KEY=your-gst-api-key

